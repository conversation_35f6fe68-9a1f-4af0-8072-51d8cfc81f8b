import { BadRequestException, ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { RegisterWithProviderDto } from './auth.dto';
import { ConfirmResetPasswordDto } from './dto/confirm-reset-password.dto';
import { UserNotFoundException } from '../common/exceptions';
import { getNow } from '../common/utils';
import { UsersService } from '../users/users.service';
import { ConfigService } from '@nestjs/config';
import PayloadType from './types/payload.type';
import { createSigner } from 'fast-jwt';
import { PrismaService } from 'nestjs-prisma';
import { randomBytes } from 'crypto';
import { hash, verify } from '@node-rs/argon2';
import RegisterUserDto from './dto/register-user.dto';
import TokensDto from './dto/tokens.dto';
import LoginUserDto from './dto/login-user.dto';
import EmailDto from './dto/email.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { EMAIL_SUBJECTS, UserProfileType } from '../common/constants';
import { ClsService } from 'nestjs-cls';
import JwtType from '../common/types/jwt-type.enum';
import messages from '../common/types/messages';
import Role from '../common/enums/role.enum';
import { UserEntity } from 'prisma/entities';

@Injectable()
export class AuthService {
	readonly #accessJwtSigner;
	readonly #refreshJwtSigner;
	readonly #verifyEmailSigner;
	readonly #resetPasswordSigner;
	readonly #verificationLinkBaseURL;
	readonly #resetPassLinkBaseURL;

	constructor(
		private readonly usersService: UsersService,
		private readonly configService: ConfigService,
		private readonly prismaService: PrismaService,
		private readonly mailerService: MailerService,
		private readonly cls: ClsService
	) {
		this.#verificationLinkBaseURL = `${this.configService.get<string>('HOST', '')}/verify-email/confirm`;
		this.#resetPassLinkBaseURL = `${this.configService.get<string>('HOST', '')}/reset-password/confirm`;

		this.#accessJwtSigner = createSigner<PayloadType>({
			key: this.configService.get<string>('JWT_SECRET'),
			algorithm: 'HS512',
			expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRES_IN') ?? '1s',
		});
		this.#refreshJwtSigner = createSigner<PayloadType>({
			key: this.configService.get<string>('JWT_SECRET'),
			algorithm: 'HS512',
			expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN') ?? '1s',
		});
		this.#verifyEmailSigner = createSigner<PayloadType>({
			key: this.configService.get<string>('JWT_SECRET'),
			algorithm: 'HS512',
			expiresIn: this.configService.get<string>('JWT_EMAIL_EXPIRES_IN') ?? '1s',
		});
		this.#resetPasswordSigner = createSigner<PayloadType>({
			key: this.configService.get<string>('JWT_SECRET'),
			algorithm: 'HS512',
			expiresIn: this.configService.get<string>('JWT_RESET_PASSWORD_EXPIRES_IN') ?? '1s',
		});
	}

	async signup(registerUserDto: RegisterUserDto) {
		const existingUser = await this.usersService.getByEmail(registerUserDto.email, {
			email: true,
			id: true,
		});

		if (existingUser) {
			throw new ConflictException(`User with such email already exists ${registerUserDto.email}`);
		}

		const userPassword = await hash(registerUserDto.password);
		const userWebsocketToken = this.#generateWebsocketToken();
		const role = registerUserDto.profile_type == UserProfileType.CANDIDATE ? Role.Candidate : Role.Recruiter;

		const createdUser = await this.usersService.create({
			email: registerUserDto.email,
			password: userPassword,
			is_admin: false,
			is_verified: false,
			dm_opened: true,
			email_verified: false,
			full_name: '',
			timezone_offset: registerUserDto.timezone_offset,
			last_active: getNow().toJSDate(),
			ws_token: userWebsocketToken,
			avatar_url: '',
		});

		await this.sendEmailVerification(createdUser, role);

		const userTokensPair: TokensDto = this.#generateTokensPairForUser(createdUser.id, role);
		return userTokensPair;
	}

	async login(loginUserDto: LoginUserDto): Promise<TokensDto> {
		const user: UserEntity | null = await this.usersService.getByEmail(loginUserDto.email, {
			email: true,
			password: true,
			id: true,
			candidate_profile: true,
			recruiter_profile: true,
		});

		if (!user) {
			throw new NotFoundException(`User not found (id=${loginUserDto.email})`);
		}

		const passwordValid = await verify(user.password, loginUserDto.password);

		if (!passwordValid) {
			throw new NotFoundException(`User not found (id=${loginUserDto.email})`);
		}

		const userTokensPair: TokensDto = this.#generateTokensPairForUser(user.id, user.candidate_profile ? Role.Candidate : Role.Recruiter);

		return userTokensPair;
	}

	async registerWithAuthProvider(registerWithProviderDto: RegisterWithProviderDto): Promise<TokensDto> {
		const userPassword = await hash(this.#generatePassword());
		const userWebsocketToken = this.#generateWebsocketToken();
		const role = registerWithProviderDto.profile_type == UserProfileType.CANDIDATE ? Role.Candidate : Role.Recruiter;

		const createdUser = await this.usersService.create({
			email: registerWithProviderDto.email,
			password: userPassword,
			is_admin: false,
			is_verified: false,
			dm_opened: true,
			email_verified: false,
			full_name: registerWithProviderDto.full_name,
			timezone_offset: 0,
			last_active: getNow().toJSDate(),
			ws_token: userWebsocketToken,
			avatar_url: '',
		});

		await this.sendEmailVerification(createdUser, role);

		const userTokensPair: TokensDto = this.#generateTokensPairForUser(createdUser.id, role);
		return userTokensPair;
	}

	async loginWithAuthProvider(loginWithProviderDto: EmailDto): Promise<TokensDto> {
		const user: UserEntity | null = await this.usersService.getByEmail(loginWithProviderDto.email, {
			email: true,
			id: true,
			candidate_profile: true,
			recruiter_profile: true,
		});

		if (!user) {
			throw new UserNotFoundException(loginWithProviderDto.email);
		}

		const userTokensPair: TokensDto = this.#generateTokensPairForUser(user.id, user.candidate_profile ? Role.Candidate : Role.Recruiter);

		return userTokensPair;
	}

	refreshAccessToken(): TokensDto {
		const context = this.cls.get('context');

		if (context.key != JwtType.Refresh) {
			throw new BadRequestException(messages.NEED_REFRESH_TOKEN);
		}

		const userTokensPair: TokensDto = this.#generateTokensPairForUser(context.user, context.role);

		return userTokensPair;
	}

	async resendVerificationEmail() {
		const context = this.cls.get('context');

		if (context.key != JwtType.Access) {
			throw new BadRequestException(messages.NO_ACCESS);
		}

		await this.sendEmailVerification(context.user, context.role);
	}

	async sendEmailVerification(user: UserEntity, role: Role) {
		const verificationToken = this.#verifyEmailSigner(new PayloadType(user.id, role, undefined, true));
		const confirmationLink = `${this.#verificationLinkBaseURL}/${verificationToken}`;

		await this.mailerService.sendMail({
			to: user.email,
			subject: EMAIL_SUBJECTS.verifyEmail,
			template: 'verify_email',
			context: {
				confirmation_link: confirmationLink,
			},
		});
	}

	async confirmEmail() {
		const context = this.cls.get('context');

		if (context.key != JwtType.Email) {
			throw new BadRequestException(messages.NEED_EMAIL_TOKEN);
		}

		await this.usersService.setUserEmail({
			userId: context.user.id,
			email: context.user.email,
			emailVerified: true,
		});
	}

	async updateEmail(newEmail: string) {
		const context = this.cls.get('context');

		if (context.key != JwtType.Access) {
			throw new BadRequestException(messages.NO_ACCESS);
		}

		if (newEmail === context.user.email) {
			throw new BadRequestException(messages.EMAIL_NOT_CHANGED);
		}

		await this.usersService.setUserEmail({
			userId: context.user.id,
			email: newEmail,
			emailVerified: false,
		});
		await this.sendEmailVerification(
			{
				...context.user,
				email: newEmail,
			},
			context.role
		);
	}

	async requestPasswordReset(resetPasswordDto: EmailDto) {
		const { email } = resetPasswordDto;
		const user: UserEntity | null = await this.usersService.getByEmail(email, {
			id: true,
			email: true,
			candidate_profile: true,
			recruiter_profile: true,
		});

		if (!user) {
			throw new UserNotFoundException(email);
		}

		const token = this.#resetPasswordSigner(
			new PayloadType(user.id, user.candidate_profile ? Role.Candidate : Role.Recruiter, undefined, true)
		);
		const link = `${this.#resetPassLinkBaseURL}/${token}`;

		await this.mailerService.sendMail({
			to: user.email,
			subject: EMAIL_SUBJECTS.resetPassword,
			template: 'reset_password',
			context: {
				confirmation_link: link,
			},
		});
	}

	async resetPassword(resetPasswordDto: ConfirmResetPasswordDto) {
		const context = this.cls.get('context');

		if (context.key != JwtType.Email) {
			throw new BadRequestException(messages.USER_NOT_FOUND);
		}

		const hashedPassword = await hash(resetPasswordDto.password);

		await this.prismaService.user.update({
			where: {
				id: context.user.id,
			},
			data: {
				password: hashedPassword,
			},
			select: {
				id: true,
			},
		});
	}

	#generatePassword() {
		return randomBytes(40).toString('hex');
	}

	#generateTokensPairForUser(userId: number, role: Role): TokensDto {
		const accessToken = this.#accessJwtSigner(new PayloadType(userId, role));
		const refreshToken = this.#refreshJwtSigner(new PayloadType(userId, role, 0));

		return {
			access_token: accessToken,
			refresh_token: refreshToken,
		};
	}

	#generateWebsocketToken() {
		return randomBytes(30).toString('hex');
	}
}
