import { ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ClsService } from 'nestjs-cls';
import { PrismaService } from 'nestjs-prisma';
import { Request } from 'express';
import JwtType from '../common/types/jwt-type.enum';
import PayloadType from './types/payload.type';
import messages from '../common/types/messages';
import Role from '../common/enums/role.enum';
import { TokenError } from 'fast-jwt';
import { FastJwtStrategy } from './strategies/fast-jwt.strategy';

@Injectable()
export class JwtAuthGuard {
	constructor(
		private readonly fastJwtStrategy: FastJwtStrategy,
		private readonly reflector: Reflector,
		private readonly cls: ClsService,
		private readonly prisma: PrismaService
	) {}

	public async canActivate(context: ExecutionContext): Promise<boolean> {
		const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [context.getHandler(), context.getClass()]);

		if (isPublic) {
			this.cls.set('context', {
				key: JwtType.None,
			});

			return true;
		}

		let payload: PayloadType;

		try {
			//await super.canActivate(context);
			const req = context.switchToHttp().getRequest<Request>();
			payload = await this.fastJwtStrategy.authenticate(req);
		} catch (error) {
			console.error('Error in JWT Auth Guard:', error);
			if (error instanceof Error && error.message === 'Auth token not found from request') {
				throw new UnauthorizedException(messages.AUTHRORIZATION_REQUIRED);
			} else if (error instanceof Object && 'message' in error) {
				throw new UnauthorizedException(error.message);
			}

			if (error instanceof TokenError) {
				throw new UnauthorizedException(messages.AUTHRORIZATION_REQUIRED);
			}
			console.error('Error in JWT Auth Guard:', error instanceof Error, error instanceof TokenError);

			throw error;
		}

		console.log('Payload:', payload);

		const isRefresh = this.reflector.getAllAndOverride<boolean>('isRefresh', [context.getHandler(), context.getClass()]);

		if (payload.r !== undefined && !isRefresh) {
			throw new UnauthorizedException(messages.NEED_ACCESS_TOKEN);
		} else if (payload.r !== undefined) {
			this.cls.set('context', { key: JwtType.Refresh, refreshId: payload.r, user: payload.user, role: payload.role });

			return true;
		}

		const declaredRoles = this.reflector.getAllAndMerge<Role[]>('listRoles', [context.getHandler(), context.getClass()]);
		const isAuthorized = this.reflector.getAllAndOverride<boolean>('isAuthorized', [context.getHandler(), context.getClass()]);
		const isEmail = this.reflector.getAllAndOverride<boolean>('isEmail', [context.getHandler(), context.getClass()]);
		const user = await this.prisma.user.findUnique({
			where: { id: payload.user },
			include: { candidate_profile: true, recruiter_profile: true },
		});

		if (!user) {
			throw new UnauthorizedException(messages.USER_NOT_FOUND);
		}

		if (payload.verifyEmail !== undefined && !isEmail) {
			throw new UnauthorizedException(messages.NEED_ACCESS_TOKEN);
		} else if (payload.verifyEmail !== undefined) {
			this.cls.set('context', { key: JwtType.Email, user, role: payload.role });

			return true;
		}

		// if (!isAuthorized && !declaredRoles.includes(payload.role)) {
		// 	throw new UnauthorizedException(messages.NO_ACCESS);
		// }

		this.cls.set('context', { key: JwtType.Access, user: user, role: payload.role });

		return true;
	}
}
