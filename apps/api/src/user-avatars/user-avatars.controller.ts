import {
	ClassSerializerInterceptor,
	Controller,
	Delete,
	FileTypeValidator,
	HttpException,
	HttpStatus,
	MaxFileSizeValidator,
	ParseFilePipe,
	Post,
	UploadedFile,
	UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

import { CannotRemoveAvatarException, CannotUploadAvatarException, NoAvatarException } from './user-avatars.exceptions';
import { UserAvatarEntity } from './user-avatar.entity';
import { UserAvatarsService } from './user-avatars.service';
import { IMAGE_EXTENSION_REGEX, MAX_IMAGE_SIZE_IN_MB } from '../common/constants';
import { User } from '../common/decorators';
import { AuthedUserGuard } from '../auth/guards';
import { RequestUser } from '../common/typings';

@UseInterceptors(ClassSerializerInterceptor)
@Controller('users/me/avatars')
export class UserAvatarsController {
	constructor(private readonly userAvatarsService: UserAvatarsService) {}

	// @AuthedUserGuard
	@Post('/upload')
	@UseInterceptors(FileInterceptor('image'))
	async upload(
		@UploadedFile(
			new ParseFilePipe({
				validators: [
					new FileTypeValidator({ fileType: IMAGE_EXTENSION_REGEX }),
					new MaxFileSizeValidator({
						maxSize: 1024 * 1024 * MAX_IMAGE_SIZE_IN_MB,
						message: 'file_too_big',
					}),
				],
			})
		)
		file: Express.Multer.File,
		@User() user: RequestUser
	) {
		try {
			const avatar = await this.userAvatarsService.upload(user.id, {
				buffer: file.buffer,
				name: String(user.id),
				size: file.size,
				mimeType: file.mimetype,
			});

			return new UserAvatarEntity(avatar);
		} catch (err) {
			if (err instanceof CannotUploadAvatarException) {
				throw new HttpException(err.code, HttpStatus.BAD_REQUEST);
			}

			throw err;
		}
	}

	@AuthedUserGuard
	@Delete()
	async remove(@User() user: RequestUser) {
		try {
			const avatar = await this.userAvatarsService.remove(user.id, user.avatar_url);
			return new UserAvatarEntity(avatar);
		} catch (err) {
			if (err instanceof CannotRemoveAvatarException || err instanceof NoAvatarException) {
				throw new HttpException(err.code, HttpStatus.BAD_REQUEST);
			}

			throw err;
		}
	}
}
